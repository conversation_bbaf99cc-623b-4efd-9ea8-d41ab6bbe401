# 待办事项 (Todo List) MVP 需求文档 (纯前端本地存储版)





## 1. 项目概述

### 1.1. 项目名称



MyTodos (本地版 MVP)



### 1.2. 项目目标



使用 Next.js 和 shadcn/ui 技术栈，快速构建一个**完全在浏览器中运行**的待办事项应用。所有数据将存储在用户的本地浏览器中，无需网络连接或用户账户。此版本的核心是验证任务“创建 -> 完成 -> 删除”的核心交互体验。



### 1.3. 核心价值



让用户能立即打开并使用一个美观、快速的界面来 **添加**、**查看** 和 **完成** 任务，所有操作都在其自己的设备上完成。

------



## 2. 功能需求 (MVP)

### 2.1. 任务管理模块 (核心功能)



- **FR-101: 创建任务**
  - 在主界面的输入框中输入 **任务标题**，按回车或点击按钮创建新任务。
  - 新任务默认为“待办 (Pending)”状态。
- **FR-102: 查看任务列表**
  - 应用加载时，从浏览器本地存储中读取并展示所有任务。
  - 任务以 **单一列表** 形式展示。
  - 每个任务项显示 **复选框** 和 **任务标题**。
- **FR-103: 更新任务状态**
  - 用户通过点击任务前的 **复选框**，将任务标记为“已完成 (Completed)”。
  - 再次点击可切换回“待办 (Pending)”状态。
  - 已完成的任务应有视觉区分（如加删除线）。
  - 所有状态变更应立即同步到本地存储。
- **FR-104: 删除任务**
  - 用户可以删除一个任务。
  - 删除前使用 **shadcn/ui 的 AlertDialog** 组件进行二次确认。
  - 任务删除后，应立即从本地存储中移除。

> **MVP 排除项**: **所有用户相关功能 (注册/登录/登出)**、**云端同步**、任务描述、截止日期、优先级、项目分组、排序等一切复杂功能。

------



## 3. 非功能性需求 (MVP)



- **NFR-01: 用户体验 (UX)**
  - 界面使用 **shadcn/ui** 组件构建，确保风格的简洁、现代和一致性。
  - 应用必须是 **响应式** 的，在桌面和移动设备上都有出色的可用性。
- **NFR-02: 性能 (Performance)**
  - 应用加载和交互必须非常迅速，因为所有操作均在本地完成。
- **NFR-03: 代码质量**
  - 整个项目必须使用 **TypeScript**，确保类型安全。

------



## 4. 技术栈实现细节



- **框架 (Framework)**: **Next.js** (App Router)。
- **UI 组件**: **shadcn/ui**。按需引入 `Button`, `Input`, `Checkbox`, `Card`, `AlertDialog` 等。
- **样式 (Styling)**: **Tailwind CSS**。
- **语言 (Language)**: **TypeScript**。
- **数据存储 (Data Persistence)**: 使用浏览器的 **`localStorage`** 进行持久化存储。
- **状态管理 (State Management)**:
  - 使用 React 的 `useState` 和 `useEffect` 钩子。
  - **强烈建议**: 创建一个自定义 Hook (例如 `useLocalStorage`) 来封装读取和写入 `localStorage` 的逻辑，并使其与 React state 自动同步。

> **注意**: 此版本**没有后端、没有数据库、没有 ORM、没有认证库**。它是一个纯粹的客户端应用。

------



## 5. 数据模型 (TypeScript Type)



由于没有数据库，我们只需定义 TypeScript 类型即可。

TypeScript

```
// types/index.ts

export type TodoStatus = 'pending' | 'completed';

export type Todo = {
  id: string; // 使用 cuid() 或 Date.now().toString() 生成唯一ID
  title: string;
  status: TodoStatus;
  createdAt: number; // 存储为时间戳 Date.now()
};
```

------



## 6. 页面与 UI/UX 设计草案 (极简版)

### 6.1. 主界面 (应用入口)



- **用户打开应用直接进入此主界面。没有登录或欢迎页面。**
- **整体布局**: 居中的单栏布局。
- **顶部**: 仅显示应用标题，如 "MyTodos"。
- **任务添加**:
  - 一个 `div` 中包含一个 `Input` 组件（用于输入任务标题）和一个 `Button` 组件（用于“添加任务”）。
- **任务列表**:
  - 下方是任务列表区域。
  - 每个任务项是一个 `div` 或 `Card`，包含：
    - 左侧是一个 `Checkbox` 组件。
    - 中间是任务标题。
    - 右侧是一个 `Button` (`variant="ghost"`, size="icon")，点击后触发 `AlertDialog` 来确认删除。
  - 当 `Checkbox` 被选中时，任务标题文本上应添加删除线样式 (`line-through`)。